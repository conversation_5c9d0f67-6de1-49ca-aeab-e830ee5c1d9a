name: Lint and Testing

on:
  push:
  pull_request:

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # all history for all branches and tags

      - name: Setup go
        uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - name: Setup golangci-lint
        uses: golangci/golangci-lint-action@v6
        with:
          version: latest
          args: --verbose

  testing:
    runs-on: ubuntu-latest
    container: golang:1.23-alpine
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # all history for all branches and tags

      - name: setup sshd server
        run: |
          apk add git make curl perl bash build-base zlib-dev ucl-dev sudo
          make ssh-server

      - name: testing
        run: |
          make test

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
